//
//  MainViewController.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/7.
//

#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import <QuartzCore/QuartzCore.h>
#import "MainViewController.h"
#import "YYBDefine.h"
#import "Masonry.h"
#import "YYBMainNavigationView.h"
#import "YYBMainContentView.h"
#import "YYBMainTopbarView.h"
#import "MainUIDefine.h"
#import "SettingPopverView.h"
#import "DownloadBoxPopver.h"
#import "YYBMainNavigationItemView.h"


@interface MainViewController () <YYBMainNavigationViewDataSource, YYBMainNavigationViewDelegate, YYBMainTopbarViewDelegate, YYBMainTopbarViewDataSource, YYBMainContentViewDelegate>

@property (nonatomic, strong) YYBMainNavigationView *navigationView;    // 左侧导航栏
@property (nonatomic, strong) YYBMainContentView *contentView;          // 右侧内容区
@property (nonatomic, strong) YYBMainTopbarView *topToolbarView;        // 顶部工具栏

@property (nonatomic, strong) YYBMainNavigationModel *navigationModel;  // 导航栏数据模型
@property (nonatomic, strong) YYBTopbarModel *topbarModel;              // 顶部工具栏数据模型

@property (strong) SettingPopverView *settingPoperView;                 //设置浮层
@property (strong) DownloadBoxPopver *downloadBoxPoperView;             //下载盒子浮层

@property (nonatomic, assign) BOOL isNavigationExpanded;                // 导航栏是否展开
@property (nonatomic, strong) MASConstraint *navigationViewWidthConstraint; // 导航栏宽度约束

@end

@implementation MainViewController

- (instancetype)init {
    if (self = [super init]) {
    
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.wantsLayer = YES;
    self.view.layer.backgroundColor = [NSColor clearColor].CGColor;
    
    [self setupModel];
    [self setupUI];
    
    [self.navigationView selectItem:self.navigationModel.items.firstObject];
}

- (void)setupModel {
    [self setupNavigationModel];
    [self setupTopbarModel];
}

- (void)setupUI {
    [self setupNavigationView];
    [self setupTopToolbarView];
    [self setupContentView];
}

#pragma mark - Model
- (void)setupNavigationModel {
    self.navigationModel = [[YYBMainNavigationModel alloc] init];
    {
        YYBMainNavigationItem *item = [YYBMainNavigationItem itemWithIdentifier:@"home"
                                                                          title:@"首页"
                                                                           icon:[NSImage imageNamed:@"Main/Navigation/HomeIcon"]
                                                                      isWebView:NO];
        [self.navigationModel addItem:item];
    }
    {
        YYBMainNavigationItem *item = [YYBMainNavigationItem itemWithIdentifier:@"rank"
                                                                          title:@"榜单"
                                                                           icon:[NSImage imageNamed:@"Main/Navigation/RankIcon"]
                                                                      isWebView:YES];
        [self.navigationModel addItem:item];
    }
    {
        YYBMainNavigationItem *item = [YYBMainNavigationItem itemWithIdentifier:@"software"
                                                                          title:@"软件"
                                                                           icon:[NSImage imageNamed:@"Main/Navigation/SoftwareIcon"]
                                                                       isWebView:YES];
        [self.navigationModel addItem:item];
    }
    {
        YYBMainNavigationItem *item = [YYBMainNavigationItem itemWithIdentifier:@"games"
                                                                          title:@"游戏"
                                                                           icon:[NSImage imageNamed:@"Main/Navigation/GameIcon"]
                                                                      isWebView:YES];
        [self.navigationModel addItem:item];
    }
}

- (void)setupTopbarModel {
    YYBTopbarModel *topbarModel = [[YYBTopbarModel alloc] init];
    topbarModel.showBackButton = YES;
    topbarModel.showSearchField = YES;
    
    {
        YYBTopbarItem *item = [[YYBTopbarItem alloc] initWithIdentifier:@"refresh"
                                                                  title:@"刷新"
                                                                   icon:[NSImage imageNamed:@"Main/TopBar/RefreshIcon"]];
        [topbarModel addItem:item];
    }
    
    {
        YYBTopbarItem *item = [[YYBTopbarItem alloc] initWithIdentifier:@"localApk"
                                                                  title:@"本地安装"
                                                                   icon:[NSImage imageNamed:@"Main/TopBar/LocalApkIcon"]];
        [topbarModel addItem:item];
    }
    
    {
        YYBTopbarItem *item = [[YYBTopbarItem alloc] initWithIdentifier:@"download"
                                                                  title:@"下载"
                                                                   icon:[NSImage imageNamed:@"Main/TopBar/DownloadIcon"]];
        [topbarModel addItem:item];
    }
    
    {
        YYBTopbarItem *item = [[YYBTopbarItem alloc] initWithIdentifier:@"message"
                                                                  title:@"消息盒子"
                                                                   icon:[NSImage imageNamed:@"Main/TopBar/MessageIcon"]];
        [topbarModel addItem:item];
    }
    
    {
        YYBTopbarItem *item = [[YYBTopbarItem alloc] initWithIdentifier:@"setting"
                                                                  title:@"设置"
                                                                   icon:[NSImage imageNamed:@"Main/TopBar/SettingIcon"]];
        [topbarModel addItem:item];
    }
    
    self.topbarModel = topbarModel;
}

#pragma mark - UI Setup Methods

- (void)setupNavigationView {
    self.navigationView = [[YYBMainNavigationView alloc] initWithFrame:NSZeroRect];
    self.navigationView.delegate = self;
    self.navigationView.dataSource = self;
    [self.view addSubview:self.navigationView];
    [self.navigationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.equalTo(self.view);
        self.navigationViewWidthConstraint = make.width.equalTo(@(kNavigationDefaultWidth));
    }];
}

- (void)setupContentView {
    self.contentView = [[YYBMainContentView alloc] initWithFrame:NSZeroRect];
    self.contentView.delegate = self;
    [self.view addSubview:self.contentView positioned:NSWindowBelow relativeTo:self.navigationView];
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.right.bottom.equalTo(self.view);
    }];
}

- (void)setupTopToolbarView {
    self.topToolbarView = [[YYBMainTopbarView alloc] initWithFrame:NSZeroRect];
    self.topToolbarView.delegate = self;
    self.topToolbarView.dataSource = self;
    [self.view addSubview:self.topToolbarView];
    [self.topToolbarView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.equalTo(self.navigationView.mas_right);
        make.right.equalTo(self.view);
        make.height.equalTo(@(kTopBarHeight));
    }];
}


#pragma mark - YYBMainNavigationViewDataSource
- (YYBMainNavigationModel *)navigationModelForNavigationView:(YYBMainNavigationView *)navigationView {
    return self.navigationModel;
}

#pragma mark - YYBMainNavigationViewDelegate
- (void)navigationView:(YYBMainNavigationView *)navigationView didSelectItem:(YYBMainNavigationItem *)item {
    [self.contentView loadNavigationItem:item animated:NO];
}

- (void)navigationViewDidClickUserAvatar:(YYBMainNavigationView *)navigationView {
    
}

- (void)navigationViewDidClickArrowButton:(YYBMainNavigationView *)navigationView {
    self.isNavigationExpanded = !self.isNavigationExpanded;
    CGFloat targetWidth = self.isNavigationExpanded ? kNavigationExpandWidth : kNavigationDefaultWidth;

    // 同时更新历史视图的展开状态
    navigationView.historyView.expanded = self.isNavigationExpanded;

    [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
        context.duration = 0.2;
        context.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
        [self.navigationViewWidthConstraint.animator setOffset:targetWidth];
    } completionHandler:nil];
}

#pragma mark - YYBMainContentViewDelegate

- (void)contentViewDidSwitchContent:(YYBMainContentView *)contentView {
    [self.topToolbarView setBackButtonEnabled:[contentView canGoBack]];
    [self.navigationView updateSelectedItem:contentView.currentNavigationItem];
}

#pragma mark - YYBMainTopbarViewDataSource

- (YYBTopbarModel *)topbarModelForTopbarView:(YYBMainTopbarView *)topbarView {
    return self.topbarModel;
}

#pragma mark - YYBMainTopbarViewDelegate

- (void)topbarViewDidClickBackButton:(YYBMainTopbarView *)topbarView {
    [self.contentView goBack];
}

- (void)topbarView:(YYBMainTopbarView *)topbarView didClickItem:(YYBTopbarItem *)item {
    // 根据item的类型执行相应的操作
    if ([item.identifier isEqualToString:@"setting"]) {
        [self showSetingPopver:topbarView];
    }
    else if ([item.identifier isEqualToString:@"download"]) {
        [self showDownloadBoxPopver:topbarView];
    }
}

- (void)topbarView:(YYBMainTopbarView *)topbarView searchTextDidChange:(NSString *)searchText {
    // 搜索文本变化处理
    NSLog(@"Search text changed: %@", searchText);
}

- (void)showSetingPopver:(NSView *)sender {
    if (self.settingPoperView && self.settingPoperView.superview) {
           [self.settingPoperView dismiss];
            self.settingPoperView = nil;
       } else {
           // 创建自定义弹窗视图
           self.settingPoperView = [[SettingPopverView alloc] init];
    
           // 显示弹窗
           [self.settingPoperView showFromView:sender inWindow:self.view.window];
       }
}

-(void)showDownloadBoxPopver:(NSView *)sender {
    if (self.downloadBoxPoperView && self.downloadBoxPoperView.view.superview) {
        [self.downloadBoxPoperView dismiss];
         self.downloadBoxPoperView = nil;
    } else {
        self.downloadBoxPoperView = [[DownloadBoxPopver alloc] init];
        [self.downloadBoxPoperView showInView:self.view atPoint:CGPointMake(self.view.frame.size.width, self.view.frame.size.height)];
    }
}

@end
