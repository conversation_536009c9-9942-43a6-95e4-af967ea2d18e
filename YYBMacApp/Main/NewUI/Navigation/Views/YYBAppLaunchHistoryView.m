//
//  YYBAppLaunchHistoryView.m
//  YYBMacApp
//
//  Created by lichenlin on 2025/8/20.
//

#import "YYBAppLaunchHistoryView.h"
#import "InstallApkInfo.h"
#import "SDWebImage.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "Masonry.h"
#import "YYBApkPackage.h"
#import "MainUIDefine.h"

static const CGFloat kItemHeight = 40.0;  // 每个展开的条目高度
static const CGFloat kItemSpacing = 8.0;  // 每个条目之间间距
static const CGFloat kIconSize = 28.0;  // 图标大小
static const CGFloat kFixedHeight = 164.0;  // view固定高度
static const CGFloat kTextLabelSpacing = 16.0;  // 文本标签与app item间隔

static NSString *const kTag = @"YYBAppLaunchHistoryView";

// 自定义的可点击 item view
@interface YYBAppLaunchHistoryItemView : NSView
@property (nonatomic, strong) InstallApkInfo *apkInfo;
@property (nonatomic, assign) BOOL expanded;
@property (nonatomic, strong) CALayer *hoverLayer;  // 专门用于 hover 效果的图层
@property (nonatomic, strong) NSView *containerView;  // 容器视图
@property (nonatomic, strong) NSView *nameLeftPaddingView;  // 名称左侧占位视图，用于动画
@property (nonatomic, strong) NSImageView *iconView;  // 图标视图
@property (nonatomic, strong) NSTextField *nameLabel;  // 名称标签
@end

@implementation YYBAppLaunchHistoryItemView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        self.wantsLayer = YES;
        self.layer.backgroundColor = [NSColor clearColor].CGColor;

        [self setupViews];

        // 创建专门的 hover 图层
        self.hoverLayer = [CALayer layer];
        self.hoverLayer.backgroundColor = [NSColor clearColor].CGColor;
        self.hoverLayer.cornerRadius = 8.0;
        self.hoverLayer.masksToBounds = YES;
        [self.layer addSublayer:self.hoverLayer];

        // 添加鼠标跟踪区域
        NSTrackingArea *trackingArea = [[NSTrackingArea alloc] initWithRect:self.bounds
                                                                    options:NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingInVisibleRect
                                                                      owner:self
                                                                   userInfo:nil];
        [self addTrackingArea:trackingArea];
    }
    return self;
}

- (void)setupViews {
    // 创建容器视图
    self.containerView = [[NSView alloc] init];
    [self addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];

    // 创建名称占位视图
    // 占位视图的宽度 = 文字的左边距，宽度会根据containerView的宽度做线性变化，用于文字位移动画
    CGFloat radio = (46 - 40) / (kNavigationExpandWidth - kNavigationDefaultWidth);
    CGFloat offset = 40 - radio * kNavigationDefaultWidth;
    self.nameLeftPaddingView = [[NSView alloc] init];
    [self.containerView addSubview:self.nameLeftPaddingView];
    [self.nameLeftPaddingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.bottom.equalTo(self.containerView);
        make.width.equalTo(self.containerView.mas_width)
            .multipliedBy(radio)
            .offset(offset);
    }];

    // 创建图标视图
    self.iconView = [[NSImageView alloc] init];
    self.iconView.imageScaling = NSImageScaleProportionallyUpOrDown;
    self.iconView.wantsLayer = YES;
    self.iconView.layer.cornerRadius = 4.0;
    self.iconView.layer.masksToBounds = YES;
    [self.containerView addSubview:self.iconView];
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.containerView).offset((kNavigationDefaultWidth - kIconSize) / 2);
        make.centerY.equalTo(self.containerView);
        make.width.height.equalTo(@(kIconSize));
    }];

    // 创建名称标签
    self.nameLabel = [[NSTextField alloc] init];
    self.nameLabel.font = [NSFont systemFontOfSize:12];
    self.nameLabel.textColor = [NSColor whiteColor];
    self.nameLabel.backgroundColor = [NSColor clearColor];
    self.nameLabel.bordered = NO;
    self.nameLabel.editable = NO;
    self.nameLabel.selectable = NO;
    self.nameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    self.nameLabel.alignment = NSTextAlignmentLeft;
    self.nameLabel.alphaValue = 0;  // 初始透明度为0
    [self.containerView addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLeftPaddingView.mas_right);
        make.right.equalTo(self.containerView).offset(-8);
        make.centerY.equalTo(self.containerView);
    }];
}

- (void)setFrameSize:(NSSize)newSize {
    [super setFrameSize:newSize];
    // 根据宽度变化调整名称文字的透明度，实现动画效果
    self.nameLabel.alphaValue = (newSize.width - kNavigationIconSize) / (kNavigationExpandWidth - kNavigationDefaultWidth);
}

- (void)updateHoverLayerFrame {
    CGFloat hoverWidth, hoverHeight;
    CGFloat x, y;

    if (self.expanded) {
        // 展开状态：116*44
        hoverWidth = 116.0;
        hoverHeight = 44.0;
        x = 0;  // 左对齐
        y = (self.bounds.size.height - hoverHeight) / 2.0;  // 垂直居中
    } else {
        // 收起状态：44*44
        hoverWidth = 44.0;
        hoverHeight = 44.0;
        x = (self.bounds.size.width - hoverWidth) / 2.0;  // 水平居中
        y = (self.bounds.size.height - hoverHeight) / 2.0;  // 垂直居中
    }

    self.hoverLayer.frame = CGRectMake(x, y, hoverWidth, hoverHeight);
}

- (void)setExpanded:(BOOL)expanded {
    _expanded = expanded;
    [self updateHoverLayerFrame];
}

- (void)layout {
    [super layout];
    [self updateHoverLayerFrame];
}

- (void)mouseEntered:(NSEvent *)event {
    // Hover 效果：border-radius: 8px; background: rgba(255, 255, 255, 0.12);
    self.hoverLayer.backgroundColor = [[NSColor colorWithWhite:1.0 alpha:0.12] CGColor];
}

- (void)mouseExited:(NSEvent *)event {
    // 恢复透明背景
    self.hoverLayer.backgroundColor = [NSColor clearColor].CGColor;
}

- (void)mouseDown:(NSEvent *)event {
    // 点击效果
    if (self.apkInfo && self.apkInfo.pkgName) {
        YYBMacLogInfo(kTag, @"点击打开APP: %@", self.apkInfo.name);
        [[YYBApkPackage shared] openApp:self.apkInfo.pkgName];
    }
}

@end

@interface YYBAppLaunchHistoryView ()

@property (nonatomic, strong) NSTextField *titleLabel;  // 标题标签
@property (nonatomic, strong) NSMutableArray<YYBAppLaunchHistoryItemView *> *itemViews;
@property (nonatomic, strong) NSView *containerView;  // 容器视图
@property (nonatomic, strong) NSView *titleLeftPaddingView;  // 标题左侧占位视图，用于动画

@end


@implementation YYBAppLaunchHistoryView


- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        _expanded = NO; // 默认收起状态
        _itemViews = [NSMutableArray array];
        [self setupView];
        [self setupNotifications];
        [self loadInitialData];
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}


- (void)setupView {
    self.wantsLayer = YES;
    self.layer.backgroundColor = [NSColor clearColor].CGColor;

    [self setupContainerView];
    [self setupTitleLabel];
    [self updateLayout];
}

- (void)setupContainerView {
    // 创建容器视图
    self.containerView = [[NSView alloc] init];
    [self addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];

    // 创建标题占位视图
    // 占位视图的宽度 = 文字的左边距，宽度会根据containerView的宽度做线性变化，用于文字位移动画
    CGFloat radio = (46 - 40) / (kNavigationExpandWidth - kNavigationDefaultWidth);
    CGFloat offset = 40 - radio * kNavigationDefaultWidth;
    self.titleLeftPaddingView = [[NSView alloc] init];
    [self.containerView addSubview:self.titleLeftPaddingView];
    [self.titleLeftPaddingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.bottom.equalTo(self.containerView);
        make.width.equalTo(self.containerView.mas_width)
            .multipliedBy(radio)
            .offset(offset);
    }];
}

- (void)setupTitleLabel {
    self.titleLabel = [[NSTextField alloc] init];
    self.titleLabel.stringValue = @"快捷启动";  // 始终显示完整文本
    self.titleLabel.font = [NSFont systemFontOfSize:12 weight:NSFontWeightRegular];
    self.titleLabel.textColor = [[NSColor whiteColor] colorWithAlphaComponent:0.45];
    self.titleLabel.backgroundColor = [NSColor clearColor];
    self.titleLabel.bordered = NO;
    self.titleLabel.editable = NO;
    self.titleLabel.selectable = NO;
    self.titleLabel.alignment = NSTextAlignmentLeft;  // 左对齐
    self.titleLabel.alphaValue = 0;  // 初始透明度为0

    [self.containerView addSubview:self.titleLabel];
}

- (void)setFrameSize:(NSSize)newSize {
    [super setFrameSize:newSize];
    // 根据宽度变化调整标题文字的透明度，实现动画效果
    self.titleLabel.alphaValue = (newSize.width - kNavigationIconSize) / (kNavigationExpandWidth - kNavigationDefaultWidth);
}

// 切换展开和收起
- (void)setExpanded:(BOOL)expanded {
    if (_expanded != expanded) {
        _expanded = expanded;
        // 重新布局、更新界面
        [self updateLayout];
    }
}

- (void)updateWithRecentApps:(NSArray<InstallApkInfo *> *)recentApps {
    // 清除旧的视图
    for (NSView *view in self.itemViews) {
        [view removeFromSuperview];
    }
    [self.itemViews removeAllObjects];

    // 最多显示3个
    NSInteger count = MIN(recentApps.count, 3);

    // 如果没有app item，隐藏整个view
    if (count == 0) {
        self.hidden = YES;
        [self updateLayout];
        return;
    } else {
        self.hidden = NO;
    }

    // 创建新的视图
    for (NSInteger i = 0; i < count; i++) {
        InstallApkInfo *apkInfo = recentApps[i];
        YYBMacLogInfo(kTag, @"刷新历史记录显示，获取到第 %ld 个最近使用的APP: %@", (long)(i+1), apkInfo.name);
        YYBAppLaunchHistoryItemView *itemView = [self createItemViewForApkInfo:apkInfo];

        [self.containerView addSubview:itemView];
        [self.itemViews addObject:itemView];
    }

    [self updateLayout];
}

#pragma mark - 通知和数据处理

- (void)setupNotifications {
    NSNotificationCenter *center = [NSNotificationCenter defaultCenter];

    // 监听APP打开通知
    [center addObserver:self
               selector:@selector(handleAppOpenedNotification:)
                   name:kYYBAppOpenedNotification
                 object:nil];

    // 监听APP卸载通知
    [center addObserver:self
               selector:@selector(handleAppUninstalledNotification:)
                   name:kYYBAppUninstalledNotification
                 object:nil];
}

- (void)loadInitialData {
    [self refreshHistoryDisplay];
}

- (void)refreshHistoryDisplay {
    // 获取最近的3个APP历史记录（从installedAppsMaps）
    NSArray<InstallApkInfo *> *recentApps = [[YYBApkPackage shared] getRecentAppsFromInstalledMaps:3];

    YYBMacLogInfo(kTag, @"刷新历史记录显示，获取到 %ld 个最近使用的APP", (long)recentApps.count);

    // 更新视图
    [self updateWithRecentApps:recentApps];
}

- (void)handleAppOpenedNotification:(NSNotification *)notification {
    InstallApkInfo *apkInfo = notification.userInfo[kYYBAppInfoKey];
    if (apkInfo) {
        YYBMacLogInfo(kTag, @"收到APP打开通知: %@", apkInfo.name);

        // 刷新历史记录显示
        dispatch_async(dispatch_get_main_queue(), ^{
            [self refreshHistoryDisplay];
        });
    }
}

- (void)handleAppUninstalledNotification:(NSNotification *)notification {
    InstallApkInfo *apkInfo = notification.userInfo[kYYBAppInfoKey];
    if (apkInfo) {
        YYBMacLogInfo(kTag, @"收到APP卸载通知: %@", apkInfo.name);

        // 刷新历史记录显示
        dispatch_async(dispatch_get_main_queue(), ^{
            [self refreshHistoryDisplay];
        });
    }
}


- (YYBAppLaunchHistoryItemView *)createItemViewForApkInfo:(InstallApkInfo *)apkInfo {
    YYBAppLaunchHistoryItemView *itemView = [[YYBAppLaunchHistoryItemView alloc] init];
    itemView.apkInfo = apkInfo;

    // 设置图标
    [itemView.iconView sd_setImageWithURL:[NSURL URLWithString:apkInfo.iconUrl]];

    // 设置名称
    itemView.nameLabel.stringValue = apkInfo.name ?: @"";

    return itemView;
}


- (void)updateLayout {

    if(self.itemViews.count == 0) {
        // view消失 - 只更新高度约束，不要清除其他约束。防止刷新向左偏移
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(@0);
        }];
        return;
    }

    // 固定整个view的高度为164 - 只更新高度约束，不要清除其他约束。防止刷新向左偏移
    [self mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@(kFixedHeight));
    }];

    // 布局标题标签，使用占位视图来实现位移动画
    [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLeftPaddingView.mas_right);
        make.top.equalTo(self.containerView);
        make.height.equalTo(@20); // 标题标签高度
    }];

    CGFloat itemHeight = self.expanded ? kItemHeight : kIconSize + 8;
    CGFloat startY = 20 + kTextLabelSpacing; // 标题高度 + 间隔

    // 布局app item，向上对齐
    for (NSInteger i = 0; i < self.itemViews.count; i++) {
        YYBAppLaunchHistoryItemView *itemView = self.itemViews[i];
        [itemView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.containerView);
            make.height.equalTo(@(itemHeight));
            make.top.equalTo(self.containerView).offset(startY + i * (itemHeight + kItemSpacing));
        }];
    }

}




@end
