//
//  YYBMainNavigationView.m
//  YYBMacApp
//
//  Created by halehuang on 2025/8/13.
//

#import "YYBMainNavigationView.h"
#import "Masonry.h"
#import "MainUIDefine.h"
#import "YYBMainNavigationItemView.h"
#import "YYBMainNavigationModel.h"
#import "YYBMainNavigationItem.h"
#import "YYBAppLaunchHistoryViewController.h"
#import "YYBAppLaunchHistoryView.h"
#import "YYBGlassEffectView.h"

static CGFloat const kIconSize = kNavigationIconSize;

@interface YYBMainNavigationView () <YYBMainNavigationItemViewDelegate>

@property (nonatomic, strong) NSView *effectView;
@property (nonatomic, strong) NSImageView *bottomImageView;
@property (nonatomic, strong) NSButton *arrowButton;
@property (nonatomic, strong) NSImageView *userImageView;
@property (nonatomic, strong) YYBMainNavigationModel *navigationModel;
@property (nonatomic, strong) NSMutableArray<YYBMainNavigationItemView *> *itemViews;
@property (nonatomic, strong) YYBAppLaunchHistoryViewController *historyViewController;

@end

@implementation YYBMainNavigationView

#pragma mark - 初始化方法

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        _itemViews = [NSMutableArray array];
        [self setupView];
    }
    return self;
}

- (void)setDataSource:(id<YYBMainNavigationViewDataSource>)dataSource {
    _dataSource = dataSource;
    [self reloadData];
}

//- (void)setupView {
//    self.wantsLayer = YES;
//    self.layer.backgroundColor = [NSColor clearColor].CGColor;
//    
//    // 创建背景图
//    self.visualEffectView = [[NSVisualEffectView alloc] initWithFrame:NSZeroRect];
//    self.visualEffectView.material = NSVisualEffectMaterialSidebar;
//    self.visualEffectView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
//    self.visualEffectView.state = NSVisualEffectStateFollowsWindowActiveState;
//    self.visualEffectView.wantsLayer = YES;
//    [self addSubview:self.visualEffectView];
//    [self.visualEffectView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.edges.equalTo(self);
//    }];
//}

- (void)setupHistoryView {
    // 创建历史记录视图控制器
    self.historyViewController = [[YYBAppLaunchHistoryViewController alloc] init];

    // 添加到视图层次结构中
    [self.effectView addSubview:self.historyViewController.view];

    // 设置约束 - 放在导航项下方
    [self.historyViewController.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.effectView);
        make.bottom.equalTo(self.arrowButton.mas_top).offset(-12); // 距离箭头12点
    }];
}

- (void)reloadData {
    if ([self.dataSource respondsToSelector:@selector(navigationModelForNavigationView:)]) {
        self.navigationModel = [self.dataSource navigationModelForNavigationView:self];
    }
    
    [self removeAllNavigationItems];
    [self addNavigationItems];
}

- (void)setupView {
    self.wantsLayer = YES;
    self.layer.backgroundColor = [NSColor clearColor].CGColor;
    [self setupEffectView];
    [self setupArrowView];
    [self setupHistoryView];
    [self setupUserView];
}

- (void)setupEffectView {
    self.effectView = [self createGlassEffectView];
    self.effectView = self.effectView ?: [self createVisualEffectView];

    [self addSubview:self.effectView];
    [self.effectView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

- (void)setupArrowView {
    // 创建箭头按钮
    NSButton *arrowButton = [[NSButton alloc] initWithFrame:NSZeroRect];
    arrowButton.wantsLayer = YES;
    arrowButton.bordered = NO;

    // 设置箭头图标
    NSImage *arrowImage = [NSImage imageNamed:@"Main/Navigation/ArrowRightIcon"];
    arrowButton.image = arrowImage;
    [arrowButton setButtonType:NSButtonTypeMomentaryChange];
    arrowImage.size = NSMakeSize(20, 20);

    // 添加到视图层次结构
    [self.effectView addSubview:arrowButton];

    // 设置约束，放在底部
    [arrowButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@((kNavigationDefaultWidth - kIconSize) / 2));
        make.bottom.equalTo(self.effectView).offset(-16);
        make.width.height.equalTo(@(kIconSize));
    }];

    // 添加点击事件
    [arrowButton setTarget:self];
    [arrowButton setAction:@selector(arrowButtonClicked:)];
    self.arrowButton = arrowButton;
}

- (void)setupUserView {
    // 创建用户头像视图
    NSImageView *userImageView = [[NSImageView alloc] initWithFrame:NSZeroRect];
    userImageView.wantsLayer = YES;
    userImageView.layer.cornerRadius = kIconSize / 2;
    userImageView.layer.masksToBounds = YES;

    // 设置默认头像图片
    NSImage *userImage = [NSImage imageNamed:@"Main/Navigation/UserIcon"];
    userImageView.image = userImage;
    userImage.size = NSMakeSize(28, 28);

    // 添加到视图层次结构
    [self.effectView addSubview:userImageView];

    // 设置约束，放在箭头按钮上方
    [userImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@((kNavigationDefaultWidth - kIconSize) / 2));
        make.size.mas_equalTo(CGSizeMake(kIconSize, kIconSize));
        make.bottom.equalTo(self.historyViewController.historyView.mas_top).offset(-12); // 距离箭头12点
    }];

    // 添加点击事件
    NSClickGestureRecognizer *clickGesture = [[NSClickGestureRecognizer alloc] initWithTarget:self action:@selector(userViewClicked:)];
    [userImageView addGestureRecognizer:clickGesture];
    self.userImageView = userImageView;
}

// 头像点击事件处理
- (void)userViewClicked:(NSGestureRecognizer *)gestureRecognizer {
    if ([self.delegate respondsToSelector:@selector(navigationViewDidClickUserAvatar:)]) {
        [self.delegate navigationViewDidClickUserAvatar:self];
    }
}

// 箭头按钮点击事件处理
- (void)arrowButtonClicked:(NSButton *)sender {
    if ([self.delegate respondsToSelector:@selector(navigationViewDidClickArrowButton:)]) {
        [self.delegate navigationViewDidClickArrowButton:self];
    }
}

- (NSView *)createVisualEffectView {
    NSVisualEffectView *visualEffectView = [[NSVisualEffectView alloc] initWithFrame:NSZeroRect];
    visualEffectView.material = NSVisualEffectMaterialUnderPageBackground;
    visualEffectView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    visualEffectView.state = NSVisualEffectStateFollowsWindowActiveState;
    visualEffectView.wantsLayer = YES;

    return visualEffectView;
}

- (NSView *)createGlassEffectView {
#if defined(MAC_OS_VERSION_26_0)
    if (@available(macOS 26.0, *)) {
        NSGlassEffectView *glassEffectView = [[NSGlassEffectView alloc] init];
        glassEffectView.style = NSGlassEffectViewStyleClear;
        glassEffectView.cornerRadius = 0;
        glassEffectView.wantsLayer = YES;
//        glassEffectView.layer.backgroundColor = [[NSColor colorNamed:@"Main/BGColor"] colorWithAlphaComponent:1].CGColor;
//        glassEffectView.tintColor = [NSColor colorWithRed:11.0 / 255.0 green:11.0 / 255.0 blue:11.0 / 255.0 alpha:1.0];
        [glassEffectView yyb_setVariant:10];
//        [glassEffectView yyb_setContentLensing:0];
//        [glassEffectView yyb_setSubvariant:@"1"];
        return glassEffectView;
    }
#endif // defined(MAC_OS_VERSION_26_0)
    return nil;
}

- (void)addNavigationItems {
    CGFloat size = kIconSize;
    CGFloat padding = (kNavigationDefaultWidth - size) / 2;
    NSArray *visibleItems = [self.navigationModel visibleItems];
    
    for (NSInteger i = 0; i < visibleItems.count; i++) {
        YYBMainNavigationItem *item = visibleItems[i];
        YYBMainNavigationItemView *itemView = [[YYBMainNavigationItemView alloc] initWithItem:item];
        itemView.delegate = self;
        
        [self.effectView addSubview:itemView];
        [self.itemViews addObject:itemView];
        
        [itemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.effectView).offset(padding);
            make.right.mas_equalTo(self.effectView).offset(-padding);
            make.top.equalTo(self.effectView).offset(88 + i * (12 + size));
            make.height.mas_equalTo(size);
        }];
    }
}

- (void)removeAllNavigationItems {
    for (YYBMainNavigationItemView *itemView in self.itemViews) {
        [itemView removeFromSuperview];
    }
    [self.itemViews removeAllObjects];
}

- (void)selectItem:(YYBMainNavigationItem *)item {
    if (item.isSelected) {
        return;
    }
    
    if ([self.delegate respondsToSelector:@selector(navigationView:didSelectItem:)]) {
        [self.delegate navigationView:self didSelectItem:item];
    }
}

- (void)updateSelectedItem:(YYBMainNavigationItem *)item {
    [self.navigationModel selectItem:item];
    for (YYBMainNavigationItemView *view in self.itemViews) {
        [view updateView];
    }
}

#pragma mark - YYBMainNavigationItemViewDelegate

- (void)navigationItemViewDidClick:(YYBMainNavigationItemView *)itemView {
    YYBMainNavigationItem *clickedItem = itemView.item;
    [self selectItem:clickedItem];
}

@end
